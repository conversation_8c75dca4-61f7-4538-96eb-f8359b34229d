import 'dart:convert';
import 'dart:io';
import 'dart:async';
import 'package:flutter/cupertino.dart';
import 'package:http/http.dart' as http;
import 'package:dio/dio.dart';
import 'package:path_provider/path_provider.dart';
import 'package:path/path.dart' as path;
import 'package:flutter/foundation.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter_cache_manager/flutter_cache_manager.dart';
import '../config/app_config.dart';
import '../models/library_item_model.dart';
import '../repositories/library_repository.dart';
import '../services/logger_service.dart';
import '../services/secure_storage_service.dart';
import 'api_service.dart';
import 'image_preloader_service.dart';

class LibraryService {
  static final LibraryRepository repository = LibraryRepository();

  // In-memory cache for library items
  static List<LibraryItem>? _cachedLibraryItems;

  // Map to store active download cancellation tokens
  static final Map<int, CancelToken> _downloadCancelTokens = {};

  // Get base URL from AppConfig
  static String get baseUrl => AppConfig.apiBaseUrl;

  // Get optimized thumbnail URL with size parameters
  // This helps reduce bandwidth by requesting smaller images
  static String getOptimizedThumbnailUrl(String thumbnailPath,
      {int width = 100, int height = 100, bool preserveRatio = true}) {
    if (thumbnailPath.isEmpty) return '';

    final url = '$baseUrl$thumbnailPath';

    // If preserveRatio is true, we'll only specify the width and let the height adjust automatically
    // This prevents stretching the image and maintains the aspect ratio
    if (preserveRatio) {
      // Check if the URL already has query parameters
      if (url.contains('?')) {
        return '$url';
      } else {
        return '$url';
      }
    } else {
      // If preserveRatio is false, specify both width and height
      // Check if the URL already has query parameters
      if (url.contains('?')) {
        return '$url&width=$width&height=$height';
      } else {
        return '$url?width=$width&height=$height';
      }
    }
  }

  // Check if an image is already cached
  static Future<bool> isImageCached(String thumbnailPath,
      {int width = 100, int height = 100, bool preserveRatio = true}) async {
    if (thumbnailPath.isEmpty) return false;

    try {
      final url = getOptimizedThumbnailUrl(thumbnailPath,
          width: width, height: height, preserveRatio: preserveRatio);
      final provider = CachedNetworkImageProvider(url);

      // Check if the image is in the cache manager
      final cacheManager = DefaultCacheManager();
      final fileInfo = await cacheManager.getFileFromCache(url);

      return fileInfo != null && fileInfo.file.existsSync();
    } catch (e) {
      LoggerService.warning('Error checking if image is cached', e);
      return false;
    }
  }

  // Get authentication headers
  static Future<Map<String, String>> _getAuthHeaders() async {
    try {
      final token = await SecureStorageService.getToken();
      if (token == null || token.isEmpty) {
        LoggerService.warning('Authentication token is null or empty');
        throw Exception('Authentication token not found');
      }
      LoggerService.debug('Authentication token retrieved successfully');
      return {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer $token',
      };
    } catch (e) {
      LoggerService.error('Error getting authentication token', e);
      throw Exception('Failed to get authentication token: ${e.toString()}');
    }
  }

  // Fetch library items from API
  static Future<List<LibraryItem>> fetchLibraryItems(
      {bool forceRefresh = false}) async {
    try {
      // Return cached data if available and not forcing refresh
      if (_cachedLibraryItems != null && !forceRefresh) {
        LoggerService.debug(
            'Returning ${_cachedLibraryItems!.length} in-memory cached library items');
        return _cachedLibraryItems!;
      }

      // If not forcing refresh, try to get library items from database first
      if (!forceRefresh) {
        final dbItems = await repository.getLibraryItems();
        if (dbItems.isNotEmpty) {
          LoggerService.debug(
              'Returning ${dbItems.length} database cached library items');
          // Update in-memory cache
          _cachedLibraryItems = dbItems;
          return dbItems;
        }
      }

      LoggerService.debug('Preparing to fetch library items from API');

      // Library items endpoint doesn't require authentication
      Map<String, String> headers = {
        'Content-Type': 'application/json',
      };
      LoggerService.debug('Using standard headers (no auth required)');

      LoggerService.info('Fetching library items from API');

      // Fetch from API with timeout
      final client = http.Client();
      http.Response response;

      try {
        response = await client
            .get(
          Uri.parse('$baseUrl${AppConfig.apiLibrary}'),
          headers: headers,
        )
            .timeout(
          const Duration(seconds: 30),
          onTimeout: () {
            LoggerService.warning('API request timed out');
            client.close();
            throw TimeoutException('API request timed out');
          },
        );
        LoggerService.debug(
            'API response received with status: ${response.statusCode}');
      } catch (e) {
        client.close();
        LoggerService.error('Failed to fetch from API', e);
        throw Exception('Failed to fetch library items: ${e.toString()}');
      } finally {
        client.close();
      }

      final statusCode = response.statusCode;

      LoggerService.debug('Processing API response');

      Map<String, dynamic> responseData;
      try {
        // Use UTF-8 decoding for proper Bengali and Burmese text support
        final data = utf8.decode(response.bodyBytes);
        responseData = jsonDecode(data);
        LoggerService.debug('Response decoded successfully');
      } catch (e) {
        LoggerService.error('Failed to decode response', e);
        throw Exception('Invalid response format');
      }

      if (responseData['status'] == 200 && responseData['payload'] != null) {
        final List<dynamic> itemsData = responseData['payload'];
        final newItems =
            itemsData.map((data) => LibraryItem.fromJson(data)).toList();

        // Get existing items from database to preserve download status
        final existingItems = await repository.getLibraryItems();

        // Create a map of existing items by ID for quick lookup
        final existingItemsMap = {
          for (var item in existingItems) item.id: item
        };

        // Merge new items with existing items to preserve download status
        final mergedItems = await Future.wait(newItems.map((newItem) async {
          final existingItem = existingItemsMap[newItem.id];
          if (existingItem != null &&
              existingItem.isDownloaded &&
              existingItem.localFilePath != null) {
            // Check if the file still exists
            final file = File(existingItem.localFilePath!);
            final fileExists = await file.exists();

            if (fileExists) {
              // Preserve download status and local file path
              return newItem.copyWith(
                isDownloaded: true,
                localFilePath: existingItem.localFilePath,
              );
            } else {
              // File doesn't exist anymore, reset download status
              LoggerService.warning(
                  'Downloaded file not found: ${existingItem.localFilePath}');
              await repository.updateDownloadStatus(
                  existingItem.id, false, null);
              return newItem;
            }
          }
          return newItem;
        }));

        // Save merged items to database
        await repository.saveLibraryItems(mergedItems);

        // Update in-memory cache
        _cachedLibraryItems = mergedItems;

        LoggerService.debug(
            'Saved ${mergedItems.length} library items to database and cache');

        // Preload thumbnail images in the background
        // We don't await this to avoid blocking the UI
        preloadThumbnailImages(mergedItems);

        return mergedItems;
      } else {
        throw Exception(
            'Failed to load library items: ${responseData['error']}');
      }
    } catch (e) {
      LoggerService.error('Error fetching library items', e);

      // If we have cached items, return them even if there was an error
      if (_cachedLibraryItems != null) {
        LoggerService.warning('Returning cached items after error');
        return _cachedLibraryItems!;
      }

      // Try to get items from database as a fallback
      final dbItems = await repository.getLibraryItems();
      if (dbItems.isNotEmpty) {
        LoggerService.warning('Returning database items after error');
        _cachedLibraryItems = dbItems;
        return dbItems;
      }

      throw Exception('Failed to load library items: ${e.toString()}');
    }
  }

  // Download a library item using Dio with progress tracking and cancellation support
  static Future<bool> downloadLibraryItem(
    LibraryItem item, {
    Function(int received, int total)? onProgress,
  }) async {
    try {
      LoggerService.info('Downloading library item: ${item.contentTitle}');

      // Get the file URL
      final fileUrl = '$baseUrl${item.contentFile}';

      // Get auth token
      final token = await SecureStorageService.getToken();

      // Create a cancel token for this download
      final cancelToken = CancelToken();
      _downloadCancelTokens[item.id] = cancelToken;

      // Create Dio instance with options
      final dio = Dio();
      dio.options.headers['Authorization'] = 'Bearer $token';
      dio.options.followRedirects = true;
      dio.options.validateStatus = (status) => status != null && status < 500;

      // Get the appropriate storage directory based on content type
      final storageDir = await _getStorageDirectory(item.contentType);

      // If we couldn't get a storage directory, create a fallback in app documents directory
      Directory targetDir;
      if (storageDir == null) {
        LoggerService.warning(
            'Using fallback directory in app documents directory');
        final appDocDir = await getApplicationDocumentsDirectory();
        if (appDocDir == null) {
          LoggerService.error('Application documents directory not available');
          return false;
        }
        targetDir = Directory('${appDocDir.path}/SCORE/Others');
        if (!await targetDir.exists()) {
          await targetDir.create(recursive: true);
        }
      } else {
        targetDir = storageDir;
      }

      // Make sure the directory exists
      if (!await targetDir.exists()) {
        await targetDir.create(recursive: true);
      }

      // Double-check that the directory was created successfully
      if (!await targetDir.exists()) {
        LoggerService.error('Failed to create directory: ${targetDir.path}');
        return false;
      }

      // Extract filename from the content file path and ensure it's valid
      String fileName = path.basename(item.contentFile);

      // If the filename is empty or invalid, create a unique filename based on item ID and content type
      if (fileName.isEmpty || fileName == '/' || !fileName.contains('.')) {
        // Generate a filename based on content type and item ID
        final extension = _getFileExtensionFromContentType(item.contentType);
        fileName =
            '${item.contentTitle.replaceAll(RegExp(r'[<>:"/\\|?*]'), '_')}_${item.id}.$extension';
        LoggerService.warning(
            'Invalid filename in URL, using generated name: $fileName');
      } else {
        // Add content title to the filename for better identification
        final extension = path.extension(fileName);
        final nameWithoutExt = path.basenameWithoutExtension(fileName);
        fileName =
            '${item.contentTitle.replaceAll(RegExp(r'[<>:"/\\|?*]'), '_')}_$nameWithoutExt$extension';
      }

      // Sanitize the filename to remove any invalid characters
      fileName = fileName.replaceAll(RegExp(r'[<>:"/\\|?*]'), '_');

      // Limit filename length to avoid path too long errors
      if (fileName.length > 100) {
        final extension = path.extension(fileName);
        fileName = '${fileName.substring(0, 96)}$extension';
      }

      // Create the local file path with the directory and filename
      final localFilePath = '${targetDir.path}/$fileName';

      // Download the file with Dio
      final response = await dio.download(
        fileUrl,
        localFilePath,
        cancelToken: cancelToken,
        onReceiveProgress: (received, total) {
          if (total != -1) {
            final progress = (received / total * 100).toStringAsFixed(0);
            LoggerService.debug('Download progress: $progress%');

            // Call the progress callback if provided
            if (onProgress != null) {
              onProgress(received, total);
            }
          }
        },
      );

      // Remove the cancel token from the map
      _downloadCancelTokens.remove(item.id);

      if (response.statusCode == 200) {
        // Verify the file was downloaded successfully
        final file = File(localFilePath);
        if (await file.exists()) {
          final fileSize = await file.length();
          if (fileSize > 0) {
            // Update the database with download status and local file path
            final result = await repository.updateDownloadStatus(
                item.id, true, localFilePath);

            // Update the item in the in-memory cache if it exists
            if (_cachedLibraryItems != null) {
              final index = _cachedLibraryItems!
                  .indexWhere((cachedItem) => cachedItem.id == item.id);
              if (index != -1) {
                _cachedLibraryItems![index] =
                    _cachedLibraryItems![index].copyWith(
                  isDownloaded: true,
                  localFilePath: localFilePath,
                );
                LoggerService.debug(
                    'Updated item in cache with download status');
              }
            }

            LoggerService.debug(
                'Downloaded library item to $localFilePath, DB update result: $result');
            return result > 0;
          } else {
            LoggerService.warning('Downloaded file is empty: $localFilePath');
            await file.delete();
            return false;
          }
        } else {
          LoggerService.warning(
              'File not found after download: $localFilePath');
          return false;
        }
      } else {
        LoggerService.warning(
            'Failed to download file: HTTP ${response.statusCode}');
        return false;
      }
    } catch (e) {
      // Remove the cancel token from the map
      _downloadCancelTokens.remove(item.id);

      // Check if the download was cancelled
      if (e is DioException && e.type == DioExceptionType.cancel) {
        LoggerService.info('Download cancelled for item: ${item.contentTitle}');
        return false;
      }

      // Provide more detailed error logging
      if (e is DioException) {
        LoggerService.error(
            'DioException while downloading library item: ${e.message}, type: ${e.type}',
            e);
      } else if (e is FileSystemException) {
        LoggerService.error(
            'FileSystemException while downloading library item: ${e.message}, path: ${e.path}, osError: ${e.osError}',
            e);
      } else {
        LoggerService.error(
            'Error downloading library item: ${e.toString()}', e);
      }
      return false;
    }
  }

  // Cancel a download in progress
  static bool cancelDownload(int itemId) {
    final cancelToken = _downloadCancelTokens[itemId];
    if (cancelToken != null && !cancelToken.isCancelled) {
      LoggerService.info('Cancelling download for item ID: $itemId');
      cancelToken.cancel('Download cancelled by user');
      _downloadCancelTokens.remove(itemId);
      return true;
    }
    return false;
  }

  // Check if a download is in progress
  static bool isDownloading(int itemId) {
    return _downloadCancelTokens.containsKey(itemId);
  }

  // Delete a downloaded library item
  static Future<bool> deleteDownloadedItem(LibraryItem item) async {
    try {
      if (item.localFilePath != null && item.isDownloaded) {
        try {
          final file = File(item.localFilePath!);

          // Delete the file if it exists
          if (await file.exists()) {
            await file.delete();
            LoggerService.debug('Deleted file: ${item.localFilePath}');
          }
        } catch (e) {
          // Log but continue to update the database
          LoggerService.warning(
              'Error deleting file: ${item.localFilePath}', e);
        }

        // Update the database regardless of whether file deletion succeeded
        final result =
            await repository.updateDownloadStatus(item.id, false, null);

        // Update the item in the in-memory cache if it exists
        if (_cachedLibraryItems != null) {
          final index = _cachedLibraryItems!
              .indexWhere((cachedItem) => cachedItem.id == item.id);
          if (index != -1) {
            _cachedLibraryItems![index] = _cachedLibraryItems![index].copyWith(
              isDownloaded: false,
              localFilePath: null,
            );
            LoggerService.debug('Updated item in cache after deletion');
          }
        }

        LoggerService.debug(
            'Deleted downloaded library item, DB update result: $result');
        return result > 0;
      }
      return false;
    } catch (e) {
      LoggerService.error('Error deleting downloaded library item', e);
      return false;
    }
  }

  // Get file size in a human-readable format
  static Future<String> getFileSize(String filePath) async {
    try {
      final file = File(filePath);
      if (await file.exists()) {
        final bytes = await file.length();
        if (bytes < 1024) return '$bytes B';
        if (bytes < 1024 * 1024)
          return '${(bytes / 1024).toStringAsFixed(1)} KB';
        if (bytes < 1024 * 1024 * 1024)
          return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
        return '${(bytes / (1024 * 1024 * 1024)).toStringAsFixed(1)} GB';
      }
      return 'Unknown';
    } catch (e) {
      LoggerService.error('Error getting file size', e);
      return 'Unknown';
    }
  }

  // Get a library item by ID
  static Future<LibraryItem?> getLibraryItemById(int id) async {
    try {
      // Check cache first
      if (_cachedLibraryItems != null) {
        final cachedItem = _cachedLibraryItems!.firstWhere(
          (item) => item.id == id,
          orElse: () => LibraryItem(
            id: -1,
            author: '',
            createdAt: '',
            contentType: '',
            contentTitle: '',
            contentLink: '',
            contentFile: '',
            contentThumbnail: '',
          ),
        );

        if (cachedItem.id != -1) {
          LoggerService.debug('Returning library item from cache: $id');
          return cachedItem;
        }
      }

      // Try to get from database
      LoggerService.debug('Getting library item from database: $id');
      return await repository.getLibraryItemById(id);
    } catch (e) {
      LoggerService.error('Error getting library item by ID', e);
      return null;
    }
  }

  // Verify all downloaded items
  static Future<void> verifyDownloadedItems() async {
    try {
      LoggerService.info('Verifying downloaded library items');

      // Get all items marked as downloaded
      final downloadedItems = await repository.getDownloadedItems();

      for (var item in downloadedItems) {
        if (item.localFilePath != null) {
          final file = File(item.localFilePath!);
          bool fileExists = false;

          try {
            fileExists = await file.exists();
          } catch (e) {
            LoggerService.warning(
                'Error checking if file exists: ${item.localFilePath}', e);
            // Continue with fileExists = false
          }

          if (!fileExists) {
            // File doesn't exist, update database
            LoggerService.warning(
                'Downloaded file not found: ${item.localFilePath}');
            await repository.updateDownloadStatus(item.id, false, null);

            // Update in-memory cache if it exists
            if (_cachedLibraryItems != null) {
              final index = _cachedLibraryItems!
                  .indexWhere((cachedItem) => cachedItem.id == item.id);
              if (index != -1) {
                _cachedLibraryItems![index] =
                    _cachedLibraryItems![index].copyWith(
                  isDownloaded: false,
                  localFilePath: null,
                );
              }
            }
          } else {
            // File exists, check if it's readable
            try {
              final fileSize = await file.length();
              if (fileSize <= 0) {
                LoggerService.warning(
                    'Downloaded file is empty: ${item.localFilePath}');
                await repository.updateDownloadStatus(item.id, false, null);

                // Update in-memory cache if it exists
                if (_cachedLibraryItems != null) {
                  final index = _cachedLibraryItems!
                      .indexWhere((cachedItem) => cachedItem.id == item.id);
                  if (index != -1) {
                    _cachedLibraryItems![index] =
                        _cachedLibraryItems![index].copyWith(
                      isDownloaded: false,
                      localFilePath: null,
                    );
                  }
                }
              }
            } catch (e) {
              LoggerService.warning(
                  'Error reading file: ${item.localFilePath}', e);
              // Keep the file marked as downloaded since it exists but might not be readable
            }
          }
        } else {
          // No file path but marked as downloaded, fix the inconsistency
          await repository.updateDownloadStatus(item.id, false, null);

          // Update in-memory cache if it exists
          if (_cachedLibraryItems != null) {
            final index = _cachedLibraryItems!
                .indexWhere((cachedItem) => cachedItem.id == item.id);
            if (index != -1) {
              _cachedLibraryItems![index] =
                  _cachedLibraryItems![index].copyWith(
                isDownloaded: false,
                localFilePath: null,
              );
            }
          }
        }
      }

      LoggerService.debug(
          'Verified ${downloadedItems.length} downloaded items');
    } catch (e) {
      LoggerService.error('Error verifying downloaded items', e);
    }
  }

  // Get the appropriate storage directory based on content type
  static Future<Directory?> _getStorageDirectory(String contentType) async {
    try {
      // Get the application documents directory (works on both iOS and Android)
      final appDocDir = await getApplicationDocumentsDirectory();
      if (appDocDir == null) {
        LoggerService.error('Application documents directory not available');
        return null;
      }

      // Create the base SCORE directory
      final scoreDir = Directory('${appDocDir.path}/SCORE');
      if (!await scoreDir.exists()) {
        await scoreDir.create(recursive: true);
      }

      // Determine the subfolder based on content type
      final contentTypeLower = contentType.toLowerCase();
      String subfolder;

      if (contentTypeLower.contains('pdf')) {
        subfolder = 'Documents';
      } else if (contentTypeLower.contains('audio') ||
          contentTypeLower.contains('mp3') ||
          contentTypeLower.contains('music')) {
        subfolder = 'Audio';
      } else if (contentTypeLower.contains('video') ||
          contentTypeLower.contains('mp4') ||
          contentTypeLower.contains('movie')) {
        subfolder = 'Videos';
      } else if (contentTypeLower.contains('image') ||
          contentTypeLower.contains('photo') ||
          contentTypeLower.contains('picture')) {
        subfolder = 'Images';
      } else {
        subfolder = 'Others';
      }

      // Create the content type subfolder
      final contentTypeDir = Directory('${scoreDir.path}/$subfolder');
      if (!await contentTypeDir.exists()) {
        await contentTypeDir.create(recursive: true);
      }

      return contentTypeDir;
    } catch (e) {
      LoggerService.error('Error getting storage directory', e);
      return null;
    }
  }

  // Helper method to determine file extension from content type
  static String _getFileExtensionFromContentType(String contentType) {
    final contentTypeLower = contentType.toLowerCase();

    if (contentTypeLower.contains('pdf')) {
      return 'pdf';
    } else if (contentTypeLower.contains('audio') ||
        contentTypeLower.contains('mp3') ||
        contentTypeLower.contains('music')) {
      return 'mp3';
    } else if (contentTypeLower.contains('video') ||
        contentTypeLower.contains('mp4') ||
        contentTypeLower.contains('movie')) {
      return 'mp4';
    } else if (contentTypeLower.contains('image') ||
        contentTypeLower.contains('photo') ||
        contentTypeLower.contains('picture')) {
      return 'jpg';
    } else if (contentTypeLower.contains('document') ||
        contentTypeLower.contains('doc') ||
        contentTypeLower.contains('text')) {
      return 'txt';
    }

    // Default to binary file
    return 'bin';
  }

  // Preload thumbnail images for faster display using the optimized service
  static Future<void> preloadThumbnailImages(List<LibraryItem> items) async {
    try {
      LoggerService.debug(
          'Preloading ${items.length} thumbnail images using ImagePreloaderService');

      // Extract thumbnail URLs from library items
      final thumbnailUrls = items
          .where((item) => item.contentThumbnail.isNotEmpty)
          .map((item) => item.contentThumbnail)
          .toList();

      // Use the optimized image preloader service
      await ImagePreloaderService.preloadLibraryThumbnails(thumbnailUrls);

      LoggerService.debug('Finished preloading thumbnail images');
    } catch (e) {
      LoggerService.error('Error preloading thumbnail images', e);
    }
  }

  // Clear cache
  static Future<bool> clearCache({bool deleteFiles = false}) async {
    try {
      // Clear in-memory cache
      _cachedLibraryItems = null;
      LoggerService.debug('In-memory library cache cleared');

      // Clear image cache
      imageCache.clear();
      imageCache.clearLiveImages();
      PaintingBinding.instance.imageCache.clear();
      PaintingBinding.instance.imageCache.clearLiveImages();

      // Clear CachedNetworkImage cache
      CachedNetworkImage.evictFromCache(baseUrl);

      // Clear flutter_cache_manager cache
      await DefaultCacheManager().emptyCache();

      // Clear ImagePreloaderService cache
      ImagePreloaderService.clearPreloadedCache();

      LoggerService.debug('Image cache cleared');

      if (deleteFiles) {
        // Get all downloaded items
        final downloadedItems = await repository.getDownloadedItems();

        // Delete each downloaded file
        for (var item in downloadedItems) {
          if (item.localFilePath != null) {
            try {
              final file = File(item.localFilePath!);
              if (await file.exists()) {
                await file.delete();
                LoggerService.debug('Deleted file: ${item.localFilePath}');
              }
            } catch (e) {
              // Log but continue with other files
              LoggerService.warning(
                  'Error deleting file: ${item.localFilePath}', e);
            }
          }
        }
      }

      // Clear database
      final result = await repository.clearLibraryItems();

      LoggerService.debug('Cleared library cache, DB result: $result');
      return result >= 0;
    } catch (e) {
      LoggerService.error('Error clearing library cache', e);
      return false;
    }
  }
}
