import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import '../../lib/repositories/enrolled_course_repository.dart';
import '../../lib/models/course_model.dart';
import '../../lib/database/database_helper.dart';

// Note: This is a simplified test without mocks for now

void main() {
  group('EnrolledCourseRepository Tests', () {
    late EnrolledCourseRepository repository;

    setUp(() {
      repository = EnrolledCourseRepository();
    });

    test('EnrolledCourse model creation works correctly', () {
      // Arrange & Act
      final course = EnrolledCourse(
        id: 1,
        completionStatus: 0.5,
        certificate: {'type': 'completion', 'url': 'https://example.com/cert'},
        modules: [
          EnrolledModule(
            id: 1,
            completionStatus: true,
            lock: false,
            state: {'progress': 100},
          ),
          EnrolledModule(
            id: 2,
            completionStatus: false,
            lock: true,
            state: {},
          ),
        ],
      );

      // Assert
      expect(course.id, equals(1));
      expect(course.completionStatus, equals(0.5));
      expect(course.certificate?['type'], equals('completion'));
      expect(course.modules.length, equals(2));
      expect(course.modules[0].completionStatus, isTrue);
      expect(course.modules[1].lock, isTrue);
    });

    test('EnrolledModule model creation works correctly', () {
      // Arrange & Act
      final module = EnrolledModule(
        id: 1,
        completionStatus: true,
        lock: false,
        state: {'progress': 100, 'score': 95},
      );

      // Assert
      expect(module.id, equals(1));
      expect(module.completionStatus, isTrue);
      expect(module.lock, isFalse);
      expect(module.state?['progress'], equals(100));
      expect(module.state?['score'], equals(95));
    });

    test('repository instance can be created', () {
      // Act & Assert
      expect(repository, isNotNull);
      expect(repository, isA<EnrolledCourseRepository>());
    });
  });
}
