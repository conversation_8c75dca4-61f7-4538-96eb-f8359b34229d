import 'package:flutter/services.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:sqflite_common_ffi/sqflite_ffi.dart';
import 'package:ZABAI/models/scorm_progress_model.dart';
import 'package:ZABAI/repositories/scorm_progress_repository.dart';
import 'package:ZABAI/services/scorm_service.dart';

void main() {
  group('SCORM Progress Tests', () {
    late ScormProgressRepository repository;
    late ScormService service;

    setUpAll(() {
      // Initialize Flutter binding for tests
      TestWidgetsFlutterBinding.ensureInitialized();

      // Initialize FFI
      sqfliteFfiInit();
      // Change the default factory for unit testing
      databaseFactory = databaseFactoryFfi;
    });

    setUp(() {
      repository = ScormProgressRepository();
      service = ScormService();
    });

    group('ScormProgress Model', () {
      test('should create ScormProgress from CMI data', () {
        final cmiData = {
          'cmi.core.lesson_status': 'incomplete',
          'cmi.core.lesson_location': '1',
          'cmi.suspend_data': 'test_data',
          'cmi.core.score.raw': '85',
        };

        final progress = ScormProgress.fromCmiData(
          userId: 1,
          moduleId: 101,
          cmiData: cmiData,
        );

        expect(progress.userId, equals(1));
        expect(progress.moduleId, equals(101));
        expect(progress.lessonStatus, equals('incomplete'));
        expect(progress.lessonLocation, equals('1'));
        expect(progress.suspendData, equals('test_data'));
        expect(progress.score['raw'], equals('85'));
        expect(progress.isCompleted, isFalse);
        expect(progress.needsSync, isTrue);
      });

      test('should detect completed status', () {
        final completedData = {
          'cmi.core.lesson_status': 'completed',
        };

        final passedData = {
          'cmi.core.lesson_status': 'passed',
        };

        final progressCompleted = ScormProgress.fromCmiData(
          userId: 1,
          moduleId: 101,
          cmiData: completedData,
        );

        final progressPassed = ScormProgress.fromCmiData(
          userId: 1,
          moduleId: 102,
          cmiData: passedData,
        );

        expect(progressCompleted.isCompleted, isTrue);
        expect(progressPassed.isCompleted, isTrue);
      });

      test('should handle sync status correctly', () {
        final progress = ScormProgress.fromCmiData(
          userId: 1,
          moduleId: 101,
          cmiData: {'test': 'data'},
        );

        expect(progress.needsSync, isTrue);
        expect(progress.syncStatus, isFalse);

        final syncedProgress = progress.markAsSynced();
        expect(syncedProgress.syncStatus, isTrue);
        expect(syncedProgress.needsSync, isFalse);
        expect(syncedProgress.syncAttempts, equals(0));

        final retriedProgress = progress.incrementSyncAttempts();
        expect(retriedProgress.syncAttempts, equals(1));
      });
    });

    group('ScormProgressRepository', () {
      test('should save and retrieve progress', () async {
        final progress = ScormProgress.fromCmiData(
          userId: 1,
          moduleId: 101,
          cmiData: {
            'cmi.core.lesson_status': 'incomplete',
            'cmi.suspend_data': 'test_data',
          },
        );

        // Save progress
        final result = await repository.saveProgress(progress);
        expect(result, greaterThan(0));

        // Retrieve progress
        final retrieved = await repository.getProgress(1, 101);
        expect(retrieved, isNotNull);
        expect(retrieved!.userId, equals(1));
        expect(retrieved.moduleId, equals(101));
        expect(retrieved.lessonStatus, equals('incomplete'));
        expect(retrieved.suspendData, equals('test_data'));
      });

      test('should update existing progress', () async {
        final initialProgress = ScormProgress.fromCmiData(
          userId: 1,
          moduleId: 101,
          cmiData: {'cmi.core.lesson_status': 'incomplete'},
        );

        // Save initial progress
        await repository.saveProgress(initialProgress);

        // Update progress
        final updatedProgress = ScormProgress.fromCmiData(
          userId: 1,
          moduleId: 101,
          cmiData: {'cmi.core.lesson_status': 'completed'},
        );

        await repository.saveProgress(updatedProgress);

        // Verify update
        final retrieved = await repository.getProgress(1, 101);
        expect(retrieved!.lessonStatus, equals('completed'));
      });

      test('should get unsynced progress', () async {
        // Save some progress records
        final progress1 = ScormProgress.fromCmiData(
          userId: 1,
          moduleId: 101,
          cmiData: {'test': 'data1'},
        );

        final progress2 = ScormProgress.fromCmiData(
          userId: 1,
          moduleId: 102,
          cmiData: {'test': 'data2'},
          syncStatus: true, // This one is synced
        );

        await repository.saveProgress(progress1);
        await repository.saveProgress(progress2);

        // Get unsynced progress
        final unsynced = await repository.getUnsyncedProgress();
        expect(unsynced.length, equals(1));
        expect(unsynced.first.moduleId, equals(101));
      });

      test('should update sync status', () async {
        final progress = ScormProgress.fromCmiData(
          userId: 1,
          moduleId: 101,
          cmiData: {'test': 'data'},
        );

        final saveResult = await repository.saveProgress(progress);
        expect(saveResult, greaterThan(0));

        // Update sync status
        await repository.updateSyncStatus(saveResult, true);

        // Verify update
        final retrieved = await repository.getProgress(1, 101);
        expect(retrieved!.syncStatus, isTrue);
      });

      test('should delete progress', () async {
        final progress = ScormProgress.fromCmiData(
          userId: 1,
          moduleId: 101,
          cmiData: {'test': 'data'},
        );

        await repository.saveProgress(progress);

        // Verify it exists
        final retrieved = await repository.getProgress(1, 101);
        expect(retrieved, isNotNull);

        // Delete it
        await repository.deleteProgress(1, 101);

        // Verify it's gone
        final deletedRetrieved = await repository.getProgress(1, 101);
        expect(deletedRetrieved, isNull);
      });

      test('should get user progress count', () async {
        // Save multiple progress records for user 1
        for (int i = 101; i <= 103; i++) {
          final progress = ScormProgress.fromCmiData(
            userId: 1,
            moduleId: i,
            cmiData: {'module': i.toString()},
          );
          await repository.saveProgress(progress);
        }

        // Save one for user 2
        final progress2 = ScormProgress.fromCmiData(
          userId: 2,
          moduleId: 104,
          cmiData: {'module': '104'},
        );
        await repository.saveProgress(progress2);

        // Check counts
        final user1Count = await repository.getProgressCount(1);
        final user2Count = await repository.getProgressCount(2);

        expect(user1Count, equals(3));
        expect(user2Count, equals(1));
      });
    });

    group('ScormService', () {
      test('should save progress locally', () async {
        final cmiData = {
          'cmi.core.lesson_status': 'incomplete',
          'cmi.suspend_data': 'test_data',
        };

        final result = await service.saveProgress(
          userId: 1,
          moduleId: 101,
          cmiData: cmiData,
        );

        expect(result, isTrue);

        // Verify it was saved
        final progress = await service.getProgress(1, 101);
        expect(progress, isNotNull);
        expect(progress!.lessonStatus, equals('incomplete'));
      });

      test('should get SCORM data as JSON string', () async {
        final cmiData = {
          'cmi.core.lesson_status': 'incomplete',
          'cmi.suspend_data': 'test_data',
        };

        await service.saveProgress(
          userId: 1,
          moduleId: 101,
          cmiData: cmiData,
        );

        final jsonData = await service.getScormDataForModule(1, 101);
        expect(jsonData, isNotEmpty);
        expect(jsonData, isNot(equals('{}')));

        // Should be valid JSON
        expect(() => Map<String, dynamic>.from(
          Map<String, dynamic>.from(
            Map<String, dynamic>.from(jsonData as Map)
          )
        ), returnsNormally);
      });

      test('should return empty object for non-existent progress', () async {
        final jsonData = await service.getScormDataForModule(999, 999);
        expect(jsonData, equals('{}'));
      });

      test('should get sync statistics', () async {
        // Save some progress records
        await service.saveProgress(
          userId: 1,
          moduleId: 101,
          cmiData: {'cmi.core.lesson_status': 'completed'},
        );

        await service.saveProgress(
          userId: 1,
          moduleId: 102,
          cmiData: {'cmi.core.lesson_status': 'incomplete'},
        );

        final stats = await service.getSyncStats(1);
        expect(stats['total'], equals(2));
        expect(stats['unsynced'], equals(2)); // Both are unsynced initially
        expect(stats['completed'], equals(1)); // One is completed
      });

      test('should clear user progress', () async {
        // Save some progress
        await service.saveProgress(
          userId: 1,
          moduleId: 101,
          cmiData: {'test': 'data'},
        );

        // Verify it exists
        final progress = await service.getProgress(1, 101);
        expect(progress, isNotNull);

        // Clear user progress
        await service.clearUserProgress(1);

        // Verify it's gone
        final clearedProgress = await service.getProgress(1, 101);
        expect(clearedProgress, isNull);
      });
    });
  });
}
