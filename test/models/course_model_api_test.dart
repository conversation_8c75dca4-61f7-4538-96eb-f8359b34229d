import 'package:flutter_test/flutter_test.dart';
import 'package:ZABAI/models/course_model.dart';

void main() {
  group('Course Model API Response Tests', () {
    test('Course.fromJson handles new API response structure', () {
      // New API response structure
      final newApiResponse = {
        "id": 1,
        "name": "Introduction to Flutter",
        "slug": "intro-flutter",
        "thumbnail": "https://example.com/flutter-thumb.jpg",
        "description": "Learn Flutter development from scratch",
        "language": "English",
        "summary": "A comprehensive course on Flutter development",
        "start_date": "2024-01-01",
        "end_date": "2024-12-31",
        "tags": ["flutter", "mobile", "development"],
        "category": ["Programming", "Mobile Development"],
        "modules": [
          {
            "id": 101,
            "name": "Getting Started",
            "slug": "getting-started",
            "description": "Introduction to Flutter basics",
            "summary": "Learn the fundamentals",
            "accessible": true,
            "dependent_module_list": [],
            "content": {
              "type": "scorm",
              "download_link": "https://example.com/module1.zip",
              "scorm_data_path": "/scorm/module1"
            }
          },
          {
            "id": 102,
            "name": "Advanced Concepts",
            "slug": "advanced-concepts",
            "description": "Advanced Flutter topics",
            "summary": "Deep dive into Flutter",
            "accessible": false,
            "dependent_module_list": [101],
            "content": {
              "type": "scorm",
              "download_link": "https://example.com/module2.zip",
              "scorm_data_path": "/scorm/module2"
            }
          }
        ]
      };

      final course = Course.fromJson(newApiResponse);

      // Test course fields
      expect(course.id, equals(1));
      expect(course.title, equals("Introduction to Flutter"));
      expect(course.slug, equals("intro-flutter"));
      expect(course.banner, equals("https://example.com/flutter-thumb.jpg"));
      expect(course.description, equals("Learn Flutter development from scratch"));
      expect(course.language, equals("English"));
      expect(course.summary, equals("A comprehensive course on Flutter development"));
      expect(course.startDate, equals("2024-01-01"));
      expect(course.endDate, equals("2024-12-31"));
      expect(course.category, equals("Programming")); // Should take first from array
      expect(course.tags, equals(["flutter", "mobile", "development"]));

      // Test modules
      expect(course.modules.length, equals(2));
      
      final module1 = course.modules[0];
      expect(module1.id, equals(101));
      expect(module1.name, equals("Getting Started"));
      expect(module1.moduleSlug, equals("getting-started"));
      expect(module1.description, equals("Introduction to Flutter basics"));
      expect(module1.summary, equals("Learn the fundamentals"));
      expect(module1.accessible, equals(true));
      expect(module1.dependentModuleList, equals([]));
      expect(module1.downloadLink, equals("https://example.com/module1.zip"));
      expect(module1.scormDataPath, equals("/scorm/module1"));
      expect(module1.contentType, equals("scorm"));

      final module2 = course.modules[1];
      expect(module2.id, equals(102));
      expect(module2.name, equals("Advanced Concepts"));
      expect(module2.moduleSlug, equals("advanced-concepts"));
      expect(module2.accessible, equals(false));
      expect(module2.dependentModuleList, equals([101]));
      expect(module2.downloadLink, equals("https://example.com/module2.zip"));
      expect(module2.scormDataPath, equals("/scorm/module2"));
      expect(module2.contentType, equals("scorm"));
    });

    test('Course.fromJson handles old API response structure (backward compatibility)', () {
      // Old API response structure
      final oldApiResponse = {
        "id": 2,
        "course_title": "React Native Basics",
        "course_slug": "react-native-basics",
        "course_banner": "https://example.com/react-thumb.jpg",
        "description": "Learn React Native development",
        "language": "English",
        "category": "Mobile Development",
        "tags": ["react", "native", "mobile"],
        "modules": [
          {
            "id": 201,
            "name": "Introduction",
            "module_slug": "introduction",
            "description": "Getting started with React Native",
            "download_link": "https://example.com/react-module1.zip",
            "scorm_data_path": "/scorm/react-module1"
          }
        ]
      };

      final course = Course.fromJson(oldApiResponse);

      // Test course fields
      expect(course.id, equals(2));
      expect(course.title, equals("React Native Basics"));
      expect(course.slug, equals("react-native-basics"));
      expect(course.banner, equals("https://example.com/react-thumb.jpg"));
      expect(course.description, equals("Learn React Native development"));
      expect(course.language, equals("English"));
      expect(course.category, equals("Mobile Development"));
      expect(course.tags, equals(["react", "native", "mobile"]));
      expect(course.summary, isNull);
      expect(course.startDate, isNull);
      expect(course.endDate, isNull);

      // Test modules
      expect(course.modules.length, equals(1));
      
      final module = course.modules[0];
      expect(module.id, equals(201));
      expect(module.name, equals("Introduction"));
      expect(module.moduleSlug, equals("introduction"));
      expect(module.description, equals("Getting started with React Native"));
      expect(module.downloadLink, equals("https://example.com/react-module1.zip"));
      expect(module.scormDataPath, equals("/scorm/react-module1"));
      expect(module.summary, isNull);
      expect(module.accessible, equals(true)); // Default value
      expect(module.dependentModuleList, equals([])); // Default value
      expect(module.contentType, isNull);
    });

    test('Course.fromJson handles mixed/partial data gracefully', () {
      final mixedApiResponse = {
        "id": 3,
        "name": "Vue.js Course", // New format
        "course_banner": "https://example.com/vue-thumb.jpg", // Old format
        "description": "Learn Vue.js framework",
        "category": [], // Empty array
        "modules": []
      };

      final course = Course.fromJson(mixedApiResponse);

      expect(course.id, equals(3));
      expect(course.title, equals("Vue.js Course"));
      expect(course.banner, equals("https://example.com/vue-thumb.jpg"));
      expect(course.category, equals("")); // Empty array should return empty string
      expect(course.modules, equals([]));
    });
  });

  group('API Response Format Tests', () {
    test('should handle direct array response format', () {
      // Direct array format like the enrolled courses API
      final directArrayResponse = [
        {
          "id": 1,
          "name": "Direct Array Course",
          "slug": "direct-array",
          "thumbnail": "https://example.com/direct.jpg",
          "description": "Course from direct array",
          "modules": []
        }
      ];

      // Should be able to parse each item in the array
      final courses = directArrayResponse
          .map((courseData) => Course.fromJson(courseData))
          .toList();

      expect(courses.length, equals(1));
      expect(courses[0].id, equals(1));
      expect(courses[0].title, equals("Direct Array Course"));
      expect(courses[0].slug, equals("direct-array"));
    });

    test('should handle structured response format', () {
      // Structured format with status and payload
      final structuredResponse = {
        "status": 200,
        "payload": {
          "courses_data": [
            {
              "id": 2,
              "name": "Structured Response Course",
              "slug": "structured-response",
              "thumbnail": "https://example.com/structured.jpg",
              "description": "Course from structured response",
              "modules": []
            }
          ]
        }
      };

      // Should be able to extract courses_data and parse
      final payload = structuredResponse['payload'] as Map<String, dynamic>;
      final coursesData = payload['courses_data'] as List;
      final courses = coursesData
          .map((courseData) => Course.fromJson(courseData))
          .toList();

      expect(courses.length, equals(1));
      expect(courses[0].id, equals(2));
      expect(courses[0].title, equals("Structured Response Course"));
      expect(courses[0].slug, equals("structured-response"));
    });
  });
}
