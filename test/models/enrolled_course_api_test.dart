import 'package:flutter_test/flutter_test.dart';
import 'package:ZABAI/models/course_model.dart';

void main() {
  group('EnrolledCourse API Response Tests', () {
    test('should handle the actual enrolled courses API response format', () {
      // This is the actual response format from the API
      final apiResponse = [
        {
          "id": 6,
          "completion_status": 100.0,
          "certificate": null,
          "modules": [
            {
              "id": 7,
              "accessible": true,
              "dependent_module_list": [],
              "content": {
                "type": "SCORM",
                "download_link": "/media/scorm/personal_hygiene_-_english_language.zip",
                "scorm_data_path": "index_lms.html"
              },
              "completion_status": true,
              "state": null
            }
          ]
        }
      ];

      // Parse the response
      final enrolledCourses = apiResponse
          .map((courseData) => EnrolledCourse.fromJson(courseData))
          .toList();

      // Verify the parsing
      expect(enrolledCourses.length, equals(1));
      
      final course = enrolledCourses[0];
      expect(course.id, equals(6));
      expect(course.completionStatus, equals(100.0));
      expect(course.certificate, isNull);
      expect(course.modules.length, equals(1));

      final module = course.modules[0];
      expect(module.id, equals(7));
      expect(module.completionStatus, equals(true));
      expect(module.lock, equals(false)); // accessible: true means lock: false
      expect(module.state, isNull);
    });

    test('should handle enrolled course with certificate', () {
      final apiResponse = [
        {
          "id": 5,
          "completion_status": 85.5,
          "certificate": {
            "type": "completion",
            "url": "https://example.com/certificate.pdf",
            "issued_date": "2024-01-15"
          },
          "modules": [
            {
              "id": 8,
              "accessible": false,
              "dependent_module_list": [7],
              "content": {
                "type": "VIDEO",
                "download_link": "/media/videos/lesson1.mp4",
                "scorm_data_path": ""
              },
              "completion_status": false,
              "state": {
                "progress": 50,
                "last_position": "00:05:30"
              }
            }
          ]
        }
      ];

      final enrolledCourses = apiResponse
          .map((courseData) => EnrolledCourse.fromJson(courseData))
          .toList();

      expect(enrolledCourses.length, equals(1));
      
      final course = enrolledCourses[0];
      expect(course.id, equals(5));
      expect(course.completionStatus, equals(85.5));
      expect(course.certificate, isNotNull);
      expect(course.certificate?['type'], equals('completion'));
      expect(course.certificate?['url'], equals('https://example.com/certificate.pdf'));

      final module = course.modules[0];
      expect(module.id, equals(8));
      expect(module.completionStatus, equals(false));
      expect(module.lock, equals(true)); // accessible: false means lock: true
      expect(module.state, isNotNull);
      expect(module.state?['progress'], equals(50));
      expect(module.state?['last_position'], equals('00:05:30'));
    });

    test('should handle empty enrolled courses response', () {
      final apiResponse = <Map<String, dynamic>>[];

      final enrolledCourses = apiResponse
          .map((courseData) => EnrolledCourse.fromJson(courseData))
          .toList();

      expect(enrolledCourses.length, equals(0));
    });

    test('should handle enrolled course with multiple modules', () {
      final apiResponse = [
        {
          "id": 10,
          "completion_status": 60.0,
          "certificate": null,
          "modules": [
            {
              "id": 11,
              "accessible": true,
              "dependent_module_list": [],
              "content": {
                "type": "SCORM",
                "download_link": "/media/scorm/module1.zip",
                "scorm_data_path": "index.html"
              },
              "completion_status": true,
              "state": {"completed": true}
            },
            {
              "id": 12,
              "accessible": true,
              "dependent_module_list": [11],
              "content": {
                "type": "SCORM",
                "download_link": "/media/scorm/module2.zip",
                "scorm_data_path": "index.html"
              },
              "completion_status": false,
              "state": {"progress": 25}
            }
          ]
        }
      ];

      final enrolledCourses = apiResponse
          .map((courseData) => EnrolledCourse.fromJson(courseData))
          .toList();

      expect(enrolledCourses.length, equals(1));
      
      final course = enrolledCourses[0];
      expect(course.id, equals(10));
      expect(course.completionStatus, equals(60.0));
      expect(course.modules.length, equals(2));

      // First module
      final module1 = course.modules[0];
      expect(module1.id, equals(11));
      expect(module1.completionStatus, equals(true));
      expect(module1.lock, equals(false));
      expect(module1.state?['completed'], equals(true));

      // Second module
      final module2 = course.modules[1];
      expect(module2.id, equals(12));
      expect(module2.completionStatus, equals(false));
      expect(module2.lock, equals(false));
      expect(module2.state?['progress'], equals(25));
    });
  });
}
