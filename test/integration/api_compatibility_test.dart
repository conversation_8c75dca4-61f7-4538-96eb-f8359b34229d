import 'package:flutter_test/flutter_test.dart';
import 'package:ZABAI/models/course_model.dart';

void main() {
  group('API Compatibility Integration Tests', () {
    test('should handle complete new API response with all fields', () {
      final newApiResponse = {
        "id": 1,
        "name": "Complete Flutter Course",
        "slug": "complete-flutter",
        "thumbnail": "https://example.com/thumb.jpg",
        "description": "Learn Flutter development",
        "language": "English",
        "summary": "Comprehensive Flutter course",
        "start_date": "2024-01-01",
        "end_date": "2024-12-31",
        "tags": ["flutter", "mobile"],
        "category": ["Programming", "Mobile"],
        "modules": [
          {
            "id": 101,
            "name": "Introduction",
            "slug": "introduction",
            "description": "Getting started",
            "summary": "Basic concepts",
            "accessible": true,
            "dependent_module_list": [],
            "content": {
              "type": "scorm",
              "download_link": "https://example.com/module1.zip",
              "scorm_data_path": "/scorm/module1"
            }
          }
        ]
      };

      final course = Course.fromJson(newApiResponse);

      // Verify all new fields are properly mapped
      expect(course.id, equals(1));
      expect(course.title, equals("Complete Flutter Course"));
      expect(course.slug, equals("complete-flutter"));
      expect(course.banner, equals("https://example.com/thumb.jpg"));
      expect(course.summary, equals("Comprehensive Flutter course"));
      expect(course.startDate, equals("2024-01-01"));
      expect(course.endDate, equals("2024-12-31"));
      expect(course.category, equals("Programming"));
      expect(course.tags, equals(["flutter", "mobile"]));

      // Verify module fields
      final module = course.modules.first;
      expect(module.id, equals(101));
      expect(module.name, equals("Introduction"));
      expect(module.moduleSlug, equals("introduction"));
      expect(module.summary, equals("Basic concepts"));
      expect(module.accessible, equals(true));
      expect(module.dependentModuleList, equals([]));
      expect(module.downloadLink, equals("https://example.com/module1.zip"));
      expect(module.scormDataPath, equals("/scorm/module1"));
      expect(module.contentType, equals("scorm"));
    });

    test('should handle legacy API response format', () {
      final legacyApiResponse = {
        "id": 2,
        "course_title": "Legacy Course",
        "course_slug": "legacy-course",
        "course_banner": "https://example.com/legacy.jpg",
        "description": "Old format course",
        "language": "English",
        "category": "Programming",
        "tags": ["legacy"],
        "modules": [
          {
            "id": 201,
            "name": "Legacy Module",
            "module_slug": "legacy-module",
            "description": "Old format module",
            "download_link": "https://example.com/legacy.zip",
            "scorm_data_path": "/scorm/legacy"
          }
        ]
      };

      final course = Course.fromJson(legacyApiResponse);

      // Verify backward compatibility
      expect(course.id, equals(2));
      expect(course.title, equals("Legacy Course"));
      expect(course.slug, equals("legacy-course"));
      expect(course.banner, equals("https://example.com/legacy.jpg"));
      expect(course.category, equals("Programming"));
      expect(course.summary, isNull);
      expect(course.startDate, isNull);
      expect(course.endDate, isNull);

      // Verify module backward compatibility
      final module = course.modules.first;
      expect(module.id, equals(201));
      expect(module.name, equals("Legacy Module"));
      expect(module.moduleSlug, equals("legacy-module"));
      expect(module.downloadLink, equals("https://example.com/legacy.zip"));
      expect(module.scormDataPath, equals("/scorm/legacy"));
      expect(module.summary, isNull);
      expect(module.accessible, equals(true)); // Default value
      expect(module.dependentModuleList, equals([])); // Default value
      expect(module.contentType, isNull);
    });

    test('should handle mixed API response formats gracefully', () {
      final mixedApiResponse = {
        "id": 3,
        "name": "Mixed Course", // New format
        "course_banner": "https://example.com/mixed.jpg", // Old format
        "description": "Mixed format course",
        "category": [], // Empty array
        "modules": [
          {
            "id": 301,
            "name": "Mixed Module",
            "slug": "mixed-module", // New format
            "description": "Mixed format module",
            "download_link": "https://example.com/mixed.zip", // Old format
            "content": { // New format
              "type": "video"
            }
          }
        ]
      };

      final course = Course.fromJson(mixedApiResponse);

      expect(course.id, equals(3));
      expect(course.title, equals("Mixed Course"));
      expect(course.banner, equals("https://example.com/mixed.jpg"));
      expect(course.category, equals("")); // Empty array handled

      final module = course.modules.first;
      expect(module.id, equals(301));
      expect(module.name, equals("Mixed Module"));
      expect(module.moduleSlug, equals("mixed-module"));
      expect(module.downloadLink, equals("https://example.com/mixed.zip"));
      expect(module.contentType, equals("video"));
    });
  });
}
