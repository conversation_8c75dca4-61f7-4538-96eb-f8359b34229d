import 'package:flutter_test/flutter_test.dart';
import 'dart:convert';

void main() {
  group('CourseService API Format Handling Tests', () {
    test('should handle direct array response format (like enrolled courses)', () {
      // This simulates the response format that was causing the error
      final directArrayResponse = '''[
        {
          "id": 6,
          "completion_status": 100.0,
          "certificate": null,
          "modules": [
            {
              "id": 7,
              "accessible": true,
              "dependent_module_list": [],
              "content": {
                "type": "SCORM",
                "download_link": "/media/scorm/personal_hygiene_-_english_language.zip",
                "scorm_data_path": "index_lms.html"
              },
              "completion_status": true,
              "state": null
            }
          ]
        }
      ]''';

      // This should not throw an error
      dynamic decodedData;
      expect(() {
        decodedData = json.decode(directArrayResponse);
      }, returnsNormally);

      // Verify it's a List
      expect(decodedData, isA<List>());
      expect(decodedData.length, equals(1));

      // Verify the structure
      final courseData = decodedData[0] as Map<String, dynamic>;
      expect(courseData['id'], equals(6));
      expect(courseData['completion_status'], equals(100.0));
    });

    test('should handle structured response format (like regular courses)', () {
      // This simulates the structured response format
      final structuredResponse = '''{
        "status": 200,
        "payload": {
          "courses_data": [
            {
              "id": 1,
              "name": "Flutter Course",
              "slug": "flutter-course",
              "thumbnail": "https://example.com/thumb.jpg",
              "description": "Learn Flutter",
              "modules": []
            }
          ]
        }
      }''';

      // This should not throw an error
      dynamic decodedData;
      expect(() {
        decodedData = json.decode(structuredResponse);
      }, returnsNormally);

      // Verify it's a Map
      expect(decodedData, isA<Map<String, dynamic>>());
      expect(decodedData['status'], equals(200));

      // Verify the nested structure
      final payload = decodedData['payload'] as Map<String, dynamic>;
      final coursesData = payload['courses_data'] as List;
      expect(coursesData.length, equals(1));

      final courseData = coursesData[0] as Map<String, dynamic>;
      expect(courseData['id'], equals(1));
      expect(courseData['name'], equals('Flutter Course'));
    });

    test('should demonstrate the fix for type checking', () {
      // Test both response formats with the same logic used in CourseService
      
      // Test 1: Direct array format
      final directArrayJson = '[{"id": 1, "name": "Course 1"}]';
      final directArrayData = json.decode(directArrayJson);
      
      List<dynamic> coursesData1;
      if (directArrayData is List) {
        coursesData1 = directArrayData;
      } else if (directArrayData is Map<String, dynamic>) {
        coursesData1 = directArrayData['payload']['courses_data'];
      } else {
        throw Exception('Invalid response format');
      }
      
      expect(coursesData1.length, equals(1));
      expect(coursesData1[0]['id'], equals(1));

      // Test 2: Structured format
      final structuredJson = '{"status": 200, "payload": {"courses_data": [{"id": 2, "name": "Course 2"}]}}';
      final structuredData = json.decode(structuredJson);
      
      List<dynamic> coursesData2;
      if (structuredData is List) {
        coursesData2 = structuredData;
      } else if (structuredData is Map<String, dynamic>) {
        coursesData2 = structuredData['payload']['courses_data'];
      } else {
        throw Exception('Invalid response format');
      }
      
      expect(coursesData2.length, equals(1));
      expect(coursesData2[0]['id'], equals(2));
    });

    test('should handle error case gracefully', () {
      // Test invalid format
      final invalidJson = '"just a string"';
      final invalidData = json.decode(invalidJson);
      
      expect(() {
        List<dynamic> coursesData;
        if (invalidData is List) {
          coursesData = invalidData;
        } else if (invalidData is Map<String, dynamic>) {
          coursesData = invalidData['payload']['courses_data'];
        } else {
          throw Exception('Invalid response format');
        }
      }, throwsException);
    });
  });
}
