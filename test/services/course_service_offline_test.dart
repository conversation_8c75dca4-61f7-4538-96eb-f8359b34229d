import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:provider/provider.dart';

import '../../lib/services/course_service.dart';
import '../../lib/repositories/enrolled_course_repository.dart';
import '../../lib/models/course_model.dart';
import '../../lib/models/user_model.dart';
import '../../lib/providers/user_provider.dart';
import '../../lib/providers/course_provider.dart';

void main() {
  group('CourseService Offline Tests', () {
    setUp(() {
      // Setup for tests
    });

    test('EnrolledCourse model can be created with proper data', () {
      // Arrange & Act
      final course = EnrolledCourse(
        id: 1,
        completionStatus: 0.5,
        modules: [
          EnrolledModule(
            id: 1,
            completionStatus: true,
            lock: false,
            state: {'progress': 100},
          ),
        ],
      );

      // Assert
      expect(course.id, equals(1));
      expect(course.completionStatus, equals(0.5));
      expect(course.modules.length, equals(1));
      expect(course.modules[0].completionStatus, isTrue);
    });

    testWidgets('CourseProvider can be created and initialized', (WidgetTester tester) async {
      // Arrange & Act
      await tester.pumpWidget(
        MaterialApp(
          home: ChangeNotifierProvider(
            create: (_) => CourseProvider(),
            child: Consumer<CourseProvider>(
              builder: (context, provider, child) {
                return Scaffold(
                  body: Text('Enrolled courses: ${provider.enrolledCourses.length}'),
                );
              },
            ),
          ),
        ),
      );

      // Assert
      expect(find.text('Enrolled courses: 0'), findsOneWidget);
    });

    test('User model can be created with required fields', () {
      // Arrange & Act
      final user = User(
        id: 1,
        username: 'testuser',
        firstName: 'Test',
        lastName: 'User',
        token: 'test_token',
        createdAt: '2023-01-01T00:00:00Z',
        updatedAt: '2023-01-01T00:00:00Z',
      );

      // Assert
      expect(user.id, equals(1));
      expect(user.username, equals('testuser'));
      expect(user.fullName, equals('Test User'));
      expect(user.token, equals('test_token'));
    });
  });
}
