import 'dart:convert';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('UTF-8 Encoding Tests', () {
    test('should properly decode Bengali text', () {
      // Sample Bengali text: "স্বাগতম" (Welcome)
      final bengaliText = 'স্বাগতম';
      
      // Encode to bytes and then decode back
      final bytes = utf8.encode(bengaliText);
      final decodedText = utf8.decode(bytes);
      
      expect(decodedText, equals(bengaliText));
      expect(decodedText, contains('স্বাগতম'));
    });

    test('should properly decode Burmese text', () {
      // Sample Burmese text: "မင်္ဂလာပါ" (Hello)
      final burmeseText = 'မင်္ဂလာပါ';
      
      // Encode to bytes and then decode back
      final bytes = utf8.encode(burmeseText);
      final decodedText = utf8.decode(bytes);
      
      expect(decodedText, equals(burmeseText));
      expect(decodedText, contains('မင်္ဂလာပါ'));
    });

    test('should handle mixed language JSON response', () {
      // Simulate API response with Bengali and Burmese text
      final mixedLanguageJson = {
        'title_bn': 'স্বাগতম',
        'title_my': 'မင်္ဂလာပါ',
        'title_en': 'Welcome',
        'description_bn': 'আপনার পছন্দের ভাষা নির্বাচন করুন',
        'description_my': 'သင်နှစ်သက်သောဘာသာစကားကိုရွေးချယ်ပါ',
        'description_en': 'Choose your preferred language'
      };

      // Convert to JSON string and then to bytes (simulating HTTP response)
      final jsonString = json.encode(mixedLanguageJson);
      final bytes = utf8.encode(jsonString);
      
      // Decode using UTF-8 (this is what we're now doing in API services)
      final decodedString = utf8.decode(bytes);
      final decodedJson = json.decode(decodedString);
      
      // Verify all text is properly decoded
      expect(decodedJson['title_bn'], equals('স্বাগতম'));
      expect(decodedJson['title_my'], equals('မင်္ဂလာပါ'));
      expect(decodedJson['title_en'], equals('Welcome'));
      expect(decodedJson['description_bn'], equals('আপনার পছন্দের ভাষা নির্বাচন করুন'));
      expect(decodedJson['description_my'], equals('သင်နှစ်သက်သောဘာသာစကားကိုရွေးချယ်ပါ'));
      expect(decodedJson['description_en'], equals('Choose your preferred language'));
    });

    test('should demonstrate the difference between response.body and utf8.decode(response.bodyBytes)', () {
      // Sample text with special characters
      final originalText = 'স্বাগতম - မင်္ဂလာပါ - Welcome';
      
      // Simulate what happens with proper UTF-8 decoding
      final bytes = utf8.encode(originalText);
      final properDecoding = utf8.decode(bytes);
      
      expect(properDecoding, equals(originalText));
      expect(properDecoding, contains('স্বাগতম'));
      expect(properDecoding, contains('မင်္ဂလာပါ'));
      expect(properDecoding, contains('Welcome'));
    });

    test('should handle course data with Bengali and Burmese content', () {
      // Simulate course API response with multilingual content
      final courseData = {
        'id': 1,
        'title': 'ব্যক্তিগত স্বাস্থ্যবিধি - Personal Hygiene',
        'description': 'এই কোর্সটি ব্যক্তিগত স্বাস্থ্যবিধি সম্পর্কে শেখায়',
        'summary': 'ကိုယ်ရေးကိုယ်တာသန့်ရှင်းရေးအကြောင်း',
        'modules': [
          {
            'name': 'পরিচ্ছন্নতার গুরুত্ব',
            'description': 'သန့်ရှင်းရေး၏အရေးကြီးမှု'
          }
        ]
      };

      // Convert to JSON and simulate HTTP response processing
      final jsonString = json.encode(courseData);
      final bytes = utf8.encode(jsonString);
      final decodedString = utf8.decode(bytes);
      final decodedData = json.decode(decodedString);

      // Verify all multilingual content is preserved
      expect(decodedData['title'], contains('ব্যক্তিগত স্বাস্থ্যবিধি'));
      expect(decodedData['description'], contains('এই কোর্সটি'));
      expect(decodedData['summary'], contains('ကိုယ်ရေးကိုယ်တာ'));
      expect(decodedData['modules'][0]['name'], contains('পরিচ্ছন্নতার'));
      expect(decodedData['modules'][0]['description'], contains('သန့်ရှင်းရေး'));
    });
  });
}
